import { useAuth } from '../contexts/AuthContext';
import { useCertificate } from '../contexts/CertificateContext';

/**
 * Hook for managing anonymous authentication flows
 */
export const useAnonymousAuth = () => {
  const { user, isAnonymous, signInAnonymously } = useAuth();
  const { createNewCertificate } = useCertificate();

  /**
   * Ensures user is authenticated (either permanent or anonymous)
   * If not authenticated, signs in anonymously
   */
  const ensureAuthenticated = async (): Promise<boolean> => {
    if (user) {
      return true; // Already authenticated
    }

    try {
      const { error } = await signInAnonymously();
      if (error) {
        console.error('Anonymous sign-in failed:', error);
        return false;
      }
      return true;
    } catch (error) {
      console.error('Anonymous sign-in error:', error);
      return false;
    }
  };

  /**
   * Creates a certificate with automatic anonymous authentication if needed
   */
  const createCertificateWithAuth = async (certificateType: string): Promise<string | null> => {
    try {
      // Ensure user is authenticated first
      const isAuthenticated = await ensureAuthenticated();
      if (!isAuthenticated) {
        throw new Error('Authentifizierung fehlgeschlagen');
      }

      // Wait a moment for auth state to update if we just signed in anonymously
      if (!user) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Create the certificate
      const certificateId = await createNewCertificate(certificateType);
      return certificateId;
    } catch (error) {
      console.error('Error creating certificate with auth:', error);
      return null;
    }
  };

  /**
   * Gets the user's email from certificate data (for anonymous users)
   */
  const getUserEmailFromCertificate = async (certificateId: string): Promise<string | null> => {
    try {
      const { supabase } = await import('../lib/supabase');
      const { data, error } = await supabase
        .from('energieausweise')
        .select('objektdaten')
        .eq('id', certificateId)
        .single();

      if (error || !data?.objektdaten) {
        return null;
      }

      const objektdaten = data.objektdaten as any;
      return objektdaten?.Kunden_email || null;
    } catch (error) {
      console.error('Error getting email from certificate:', error);
      return null;
    }
  };

  return {
    user,
    isAnonymous,
    ensureAuthenticated,
    createCertificateWithAuth,
    getUserEmailFromCertificate,
  };
};
